#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص شامل لنظام إدارة الصناديق البريدية
Comprehensive System Check for Postal Box Management
"""

import os
import sys
import sqlite3
import importlib.util
from pathlib import Path

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    print(f"   إصدار Python: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        return False
    else:
        print("✅ إصدار Python مناسب")
        return True

def check_required_modules():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_modules = [
        'flask', 'flask_sqlalchemy', 'flask_login',
        'pandas', 'openpyxl', 'xlsxwriter', 'werkzeug',
        'apscheduler', 'psutil'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - مفقود")
            missing_modules.append(module)
    
    return len(missing_modules) == 0, missing_modules

def check_database():
    """فحص قاعدة البيانات"""
    print("\n🗄️ فحص قاعدة البيانات...")
    
    db_path = "instance/postal_box_management.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول الأساسية
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = [row[0] for row in cursor.fetchall()]
        
        required_tables = ['users', 'subscribers', 'subscriptions', 'notifications']
        missing_tables = []
        
        for table in required_tables:
            if table in tables:
                print(f"✅ جدول {table}")
            else:
                print(f"❌ جدول {table} - مفقود")
                missing_tables.append(table)
        
        conn.close()
        
        if missing_tables:
            print(f"⚠️ جداول مفقودة: {', '.join(missing_tables)}")
            return False
        else:
            print("✅ قاعدة البيانات سليمة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_file_structure():
    """فحص بنية الملفات"""
    print("\n📁 فحص بنية الملفات...")
    
    required_structure = {
        'files': [
            'app.py', 'run.py', 'config.py', 'requirements.txt'
        ],
        'directories': [
            'app', 'app/templates', 'app/static', 'instance',
            'app/main', 'app/subscribers', 'app/reports',
            'app/auth', 'app/notifications_bp', 'app/backup_bp'
        ],
        'app_files': [
            'app/__init__.py', 'app/models.py', 'app/extensions.py',
            'app/backup.py', 'app/scheduler.py', 'app/notifications.py'
        ]
    }
    
    issues = []
    
    # فحص الملفات الأساسية
    for file_path in required_structure['files']:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            issues.append(f"ملف مفقود: {file_path}")
    
    # فحص المجلدات
    for dir_path in required_structure['directories']:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - مفقود")
            issues.append(f"مجلد مفقود: {dir_path}")
    
    # فحص ملفات التطبيق
    for file_path in required_structure['app_files']:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            issues.append(f"ملف مفقود: {file_path}")
    
    return len(issues) == 0, issues

def check_permissions():
    """فحص الصلاحيات"""
    print("\n🔐 فحص الصلاحيات...")
    
    paths_to_check = [
        'instance', 'instance/uploads', 'uploads'
    ]
    
    issues = []
    
    for path in paths_to_check:
        if os.path.exists(path):
            if os.access(path, os.R_OK | os.W_OK):
                print(f"✅ {path} - صلاحيات القراءة والكتابة")
            else:
                print(f"❌ {path} - صلاحيات غير كافية")
                issues.append(f"صلاحيات غير كافية: {path}")
        else:
            try:
                os.makedirs(path, exist_ok=True)
                print(f"✅ تم إنشاء {path}")
            except Exception as e:
                print(f"❌ خطأ في إنشاء {path}: {e}")
                issues.append(f"خطأ في إنشاء {path}: {e}")
    
    return len(issues) == 0, issues

def test_app_import():
    """اختبار استيراد التطبيق"""
    print("\n🧪 اختبار استيراد التطبيق...")
    
    try:
        # اختبار استيراد الملفات الأساسية
        import config
        print("✅ config.py")
        
        from app import create_app
        print("✅ app.py")
        
        from app.extensions import db, login_manager
        print("✅ app/extensions.py")
        
        from app.models import User, Subscriber
        print("✅ app/models.py")
        
        # اختبار إنشاء التطبيق
        app = create_app('testing')
        print("✅ إنشاء التطبيق")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 70)
    print("🔍 فحص شامل لنظام إدارة الصناديق البريدية")
    print("=" * 70)
    
    all_checks_passed = True
    
    # فحص إصدار Python
    if not check_python_version():
        all_checks_passed = False
    
    # فحص المكتبات
    modules_ok, missing_modules = check_required_modules()
    if not modules_ok:
        all_checks_passed = False
        print(f"\n⚠️ لتثبيت المكتبات المفقودة، شغل: pip install {' '.join(missing_modules)}")
    
    # فحص بنية الملفات
    structure_ok, structure_issues = check_file_structure()
    if not structure_ok:
        all_checks_passed = False
    
    # فحص الصلاحيات
    permissions_ok, permission_issues = check_permissions()
    if not permissions_ok:
        all_checks_passed = False
    
    # فحص قاعدة البيانات
    if not check_database():
        all_checks_passed = False
    
    # اختبار استيراد التطبيق
    if not test_app_import():
        all_checks_passed = False
    
    print("\n" + "=" * 70)
    print("📊 تقرير الفحص النهائي:")
    
    if all_checks_passed:
        print("🎉 جميع الفحوصات نجحت! النظام جاهز للتشغيل.")
        print("\n🚀 لتشغيل النظام:")
        print("   python run.py")
        print("   أو")
        print("   python app.py")
    else:
        print("⚠️ يوجد مشاكل تحتاج إلى إصلاح قبل تشغيل النظام.")
        
        if not modules_ok:
            print(f"\n📦 قم بتثبيت المكتبات المفقودة:")
            print(f"   pip install {' '.join(missing_modules)}")
        
        if not structure_ok:
            print(f"\n📁 مشاكل في بنية الملفات:")
            for issue in structure_issues:
                print(f"   - {issue}")
        
        if not permissions_ok:
            print(f"\n🔐 مشاكل في الصلاحيات:")
            for issue in permission_issues:
                print(f"   - {issue}")
    
    print("=" * 70)

if __name__ == '__main__':
    main()
