#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشخيص متقدمة لنظام إدارة الصناديق البريدية
Advanced Diagnostic Tool for Postal Box Management System
"""

import os
import sys
import socket
import subprocess
import platform
import sqlite3
import importlib.util
from pathlib import Path

def print_header(title):
    """طباعة عنوان مع تنسيق"""
    print("\n" + "=" * 70)
    print(f"🔍 {title}")
    print("=" * 70)

def check_system_info():
    """فحص معلومات النظام"""
    print_header("معلومات النظام")
    
    print(f"🖥️ نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"🏗️ المعمارية: {platform.architecture()[0]}")
    print(f"🐍 إصدار Python: {sys.version}")
    print(f"📁 مجلد العمل: {os.getcwd()}")
    print(f"🔧 مسار Python: {sys.executable}")

def check_network():
    """فحص الشبكة والمنافذ"""
    print_header("فحص الشبكة والمنافذ")
    
    # فحص المنفذ 5000
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("⚠️ المنفذ 5000 مستخدم بالفعل")
            
            # محاولة معرفة العملية التي تستخدم المنفذ
            try:
                if platform.system() == "Windows":
                    result = subprocess.run(['netstat', '-ano'], capture_output=True, text=True)
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ':5000' in line and 'LISTENING' in line:
                            print(f"📊 تفاصيل المنفذ: {line.strip()}")
                else:
                    result = subprocess.run(['lsof', '-i', ':5000'], capture_output=True, text=True)
                    print(f"📊 العمليات على المنفذ 5000:\n{result.stdout}")
            except:
                pass
        else:
            print("✅ المنفذ 5000 متاح")
            
    except Exception as e:
        print(f"❌ خطأ في فحص المنفذ: {e}")

def check_python_modules():
    """فحص مفصل للمكتبات"""
    print_header("فحص المكتبات المفصل")
    
    modules = {
        'flask': 'Flask',
        'flask_sqlalchemy': 'Flask-SQLAlchemy', 
        'flask_login': 'Flask-Login',
        'pandas': 'Pandas',
        'openpyxl': 'OpenPyXL',
        'xlsxwriter': 'XlsxWriter',
        'werkzeug': 'Werkzeug',
        'apscheduler': 'APScheduler',
        'psutil': 'PSUtil'
    }
    
    for module, name in modules.items():
        try:
            mod = importlib.import_module(module)
            version = getattr(mod, '__version__', 'غير معروف')
            print(f"✅ {name}: {version}")
        except ImportError as e:
            print(f"❌ {name}: غير مثبت - {e}")
        except Exception as e:
            print(f"⚠️ {name}: مشكلة - {e}")

def check_database_detailed():
    """فحص مفصل لقاعدة البيانات"""
    print_header("فحص قاعدة البيانات المفصل")
    
    db_path = "instance/postal_box_management.db"
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        # معلومات الملف
        stat = os.stat(db_path)
        print(f"📁 حجم قاعدة البيانات: {stat.st_size} بايت")
        print(f"📅 آخر تعديل: {stat.st_mtime}")
        
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"\n📋 الجداول الموجودة ({len(tables)}):")
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📊 {table_name}: {count} سجل")
        
        # فحص المستخدمين
        try:
            cursor.execute("SELECT username, email, is_admin FROM users")
            users = cursor.fetchall()
            print(f"\n👥 المستخدمين ({len(users)}):")
            for user in users:
                admin_status = "مدير" if user[2] else "مستخدم عادي"
                print(f"   👤 {user[0]} ({user[1]}) - {admin_status}")
        except:
            print("⚠️ لا يمكن قراءة جدول المستخدمين")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def test_app_startup():
    """اختبار بدء التطبيق"""
    print_header("اختبار بدء التطبيق")
    
    try:
        # اختبار استيراد التطبيق
        print("🔄 اختبار استيراد التطبيق...")
        from app import create_app
        print("✅ تم استيراد create_app")
        
        # اختبار إنشاء التطبيق
        print("🔄 اختبار إنشاء التطبيق...")
        app = create_app('testing')
        print("✅ تم إنشاء التطبيق")
        
        # اختبار السياق
        print("🔄 اختبار سياق التطبيق...")
        with app.app_context():
            from app.extensions import db
            print("✅ تم تحميل قاعدة البيانات")
            
            from app.models import User, Subscriber
            print("✅ تم تحميل النماذج")
        
        print("✅ جميع اختبارات بدء التطبيق نجحت")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بدء التطبيق: {e}")
        import traceback
        print("📋 تفاصيل الخطأ:")
        traceback.print_exc()
        return False

def check_file_permissions():
    """فحص صلاحيات الملفات"""
    print_header("فحص صلاحيات الملفات")
    
    important_paths = [
        'app.py', 'run.py', 'config.py',
        'instance/', 'instance/postal_box_management.db',
        'app/', 'app/templates/', 'app/static/'
    ]
    
    for path in important_paths:
        if os.path.exists(path):
            readable = os.access(path, os.R_OK)
            writable = os.access(path, os.W_OK)
            executable = os.access(path, os.X_OK)
            
            permissions = []
            if readable: permissions.append("قراءة")
            if writable: permissions.append("كتابة")
            if executable: permissions.append("تنفيذ")
            
            status = "✅" if readable else "❌"
            print(f"{status} {path}: {', '.join(permissions) if permissions else 'لا توجد صلاحيات'}")
        else:
            print(f"❌ {path}: غير موجود")

def run_quick_test():
    """اختبار سريع للنظام"""
    print_header("اختبار سريع للنظام")
    
    try:
        # محاولة تشغيل سريع
        print("🔄 محاولة تشغيل سريع للنظام...")
        
        result = subprocess.run([
            sys.executable, '-c', 
            '''
import sys
sys.path.insert(0, ".")
from app import create_app
app = create_app("testing")
print("SUCCESS: النظام يعمل بشكل طبيعي")
'''
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ الاختبار السريع نجح")
            print(f"📤 الإخراج: {result.stdout.strip()}")
        else:
            print("❌ الاختبار السريع فشل")
            print(f"📤 الخطأ: {result.stderr.strip()}")
            
    except subprocess.TimeoutExpired:
        print("⏰ انتهت مهلة الاختبار السريع")
    except Exception as e:
        print(f"❌ خطأ في الاختبار السريع: {e}")

def main():
    """الدالة الرئيسية"""
    print("🔍 أداة التشخيص المتقدمة لنظام إدارة الصناديق البريدية")
    print("=" * 70)
    
    # تشغيل جميع الفحوصات
    check_system_info()
    check_network()
    check_python_modules()
    check_file_permissions()
    check_database_detailed()
    test_app_startup()
    run_quick_test()
    
    print_header("ملخص التشخيص")
    print("✅ تم الانتهاء من جميع الفحوصات")
    print("📋 راجع النتائج أعلاه لتحديد أي مشاكل")
    print("💡 إذا كان كل شيء يبدو طبيعياً، جرب تشغيل النظام مرة أخرى")

if __name__ == '__main__':
    main()
