#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مبسط لنظام إدارة الصناديق البريدية
Simple launcher for Postal Box Management System
"""

import os
import sys
import webbrowser
import threading
import time

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح على: http://localhost:5000")
    except:
        print("⚠️ لم يتم فتح المتصفح تلقائياً")
        print("🌐 افتح المتصفح يدوياً على: http://localhost:5000")

def main():
    """الدالة الرئيسية"""
    print("🚀 تشغيل نظام إدارة الصناديق البريدية")
    print("=" * 50)
    
    try:
        # استيراد التطبيق
        from app import create_app
        
        # إنشاء التطبيق
        app = create_app('development')
        
        print("✅ تم تحميل النظام بنجاح")
        print("🌐 العنوان: http://localhost:5000")
        print("👤 المستخدم: admin")
        print("🔑 كلمة المرور: admin123")
        print("=" * 50)
        print("⏳ بدء الخادم...")
        
        # بدء خيط فتح المتصفح
        browser_thread = threading.Thread(target=open_browser_delayed)
        browser_thread.daemon = True
        browser_thread.start()
        
        # تشغيل التطبيق
        app.run(
            debug=False,
            host='0.0.0.0',
            port=5000,
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف النظام")
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        print("💡 جرب تشغيل: python diagnose_system.py")

if __name__ == '__main__':
    main()
