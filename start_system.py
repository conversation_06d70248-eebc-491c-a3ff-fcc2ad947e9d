#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام إدارة الصناديق البريدية مع فحص شامل
Enhanced System Launcher with Health Check
"""

import os
import sys
import time
import webbrowser
import threading
from app import create_app
from app.extensions import db

def pre_launch_check():
    """فحص ما قبل التشغيل"""
    print("🔍 فحص النظام قبل التشغيل...")
    
    # فحص قاعدة البيانات
    try:
        app = create_app('development')
        with app.app_context():
            # اختبار الاتصال بقاعدة البيانات
            with db.engine.connect() as connection:
                connection.execute(db.text('SELECT 1'))
            print("✅ قاعدة البيانات متصلة")
            
            # فحص الجداول
            from app.models import User, Subscriber, Subscription, Notification
            user_count = User.query.count()
            subscriber_count = Subscriber.query.count()
            subscription_count = Subscription.query.count()
            notification_count = Notification.query.count()
            
            print(f"📊 إحصائيات قاعدة البيانات:")
            print(f"   👥 المستخدمين: {user_count}")
            print(f"   📮 المشتركين: {subscriber_count}")
            print(f"   📋 الاشتراكات: {subscription_count}")
            print(f"   🔔 الإشعارات: {notification_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص قاعدة البيانات: {e}")
        return False

def open_browser_delayed():
    """فتح المتصفح بعد تأخير"""
    time.sleep(3)  # انتظار 3 ثوان لبدء الخادم
    try:
        webbrowser.open('http://localhost:5000')
        print("🌐 تم فتح المتصفح تلقائياً")
    except Exception as e:
        print(f"⚠️ لم يتم فتح المتصفح تلقائياً: {e}")

def display_system_info():
    """عرض معلومات النظام"""
    print("\n" + "=" * 70)
    print("🚀 نظام إدارة الصناديق البريدية")
    print("=" * 70)
    print("📍 عنوان النظام: http://localhost:5000")
    print("📍 عنوان بديل: http://127.0.0.1:5000")
    print("\n👤 بيانات تسجيل الدخول الافتراضية:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("\n🔧 أوامر مفيدة:")
    print("   Ctrl+C : إيقاف الخادم")
    print("   F5     : تحديث الصفحة")
    print("=" * 70)

def main():
    """الدالة الرئيسية"""
    print("🌟 مرحباً بك في نظام إدارة الصناديق البريدية")
    print("=" * 70)
    
    # فحص ما قبل التشغيل
    if not pre_launch_check():
        print("❌ فشل في فحص النظام. يرجى تشغيل: python init_database.py")
        sys.exit(1)
    
    print("✅ جميع الفحوصات نجحت!")
    
    # عرض معلومات النظام
    display_system_info()
    
    # بدء خيط فتح المتصفح
    browser_thread = threading.Thread(target=open_browser_delayed)
    browser_thread.daemon = True
    browser_thread.start()
    
    try:
        # إنشاء وتشغيل التطبيق
        app = create_app('development')
        
        print("\n🚀 بدء تشغيل الخادم...")
        print("⏳ انتظر قليلاً...")
        
        # تشغيل التطبيق
        app.run(
            debug=False,  # إيقاف وضع التطوير لتجنب الرسائل الإضافية
            host='0.0.0.0',
            port=5000,
            use_reloader=False  # إيقاف إعادة التحميل التلقائي
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 تم إيقاف الخادم بواسطة المستخدم")
        print("👋 شكراً لاستخدام نظام إدارة الصناديق البريدية")
        
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        print("💡 تأكد من أن المنفذ 5000 غير مستخدم")
        sys.exit(1)

if __name__ == '__main__':
    main()
