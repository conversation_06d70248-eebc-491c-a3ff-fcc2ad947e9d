#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات وإعداد الجداول
Database Initialization Script
"""

import os
import sys
from app import create_app
from app.extensions import db
from app.models import User, Subscriber, Subscription, Notification

def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    print("🗄️ إنشاء قاعدة البيانات...")
    
    # إنشاء التطبيق
    app = create_app('development')
    
    with app.app_context():
        try:
            # إنشاء جميع الجداول
            db.create_all()
            print("✅ تم إنشاء جميع الجداول بنجاح")
            
            # فحص الجداول المنشأة
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            print("\n📋 الجداول المنشأة:")
            for table in tables:
                print(f"   ✅ {table}")
            
            # إنشاء مستخدم افتراضي إذا لم يكن موجوداً
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("\n👤 تم إنشاء المستخدم الافتراضي:")
                print("   اسم المستخدم: admin")
                print("   كلمة المرور: admin123")
            else:
                print("\n👤 المستخدم الافتراضي موجود مسبقاً")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🚀 إعداد قاعدة البيانات لنظام إدارة الصناديق البريدية")
    print("=" * 60)
    
    if init_database():
        print("\n" + "=" * 60)
        print("✅ تم إعداد قاعدة البيانات بنجاح!")
        print("🎉 النظام جاهز للتشغيل الآن")
        print("\n🚀 لتشغيل النظام:")
        print("   python run.py")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ فشل في إعداد قاعدة البيانات")
        print("=" * 60)
        sys.exit(1)

if __name__ == '__main__':
    main()
