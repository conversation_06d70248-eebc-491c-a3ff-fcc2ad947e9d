# دليل استكشاف الأخطاء - نظام إدارة الصناديق البريدية

## 🔍 التشخيص الأولي

**النظام يعمل بشكل مثالي!** جميع الاختبارات تظهر أن النظام سليم ويعمل بدون أخطاء.

---

## 🚀 طرق التشغيل المختلفة

### 1. التشغيل المبسط (الأسهل)
```bash
python simple_start.py
```

### 2. التشغيل المحسن
```bash
python start_system.py
```

### 3. التشغيل التقليدي
```bash
python run.py
```

### 4. التشغيل المباشر
```bash
python app.py
```

### 5. استخدام ملفات Batch
```
تشغيل_النظام.bat
```

---

## 🔧 إذا لم يعمل النظام

### الخطوة 1: تشخيص شامل
```bash
python diagnose_system.py
```

### الخطوة 2: فحص النظام
```bash
python system_check.py
```

### الخطوة 3: تنظيف النظام
```bash
python clean_system.py
```

### الخطوة 4: إعادة إنشاء قاعدة البيانات
```bash
python init_database.py
```

---

## ⚠️ المشاكل الشائعة والحلول

### 1. خطأ "المنفذ مستخدم"
**المشكلة**: `Address already in use`
**الحل**:
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID [رقم_العملية] /F

# أو استخدم منفذ مختلف
python app.py --port 5001
```

### 2. خطأ "مكتبة مفقودة"
**المشكلة**: `ModuleNotFoundError`
**الحل**:
```bash
pip install -r requirements.txt
# أو
pip install flask flask-sqlalchemy flask-login pandas openpyxl
```

### 3. خطأ قاعدة البيانات
**المشكلة**: `database is locked` أو `no such table`
**الحل**:
```bash
python init_database.py
```

### 4. خطأ الصلاحيات
**المشكلة**: `Permission denied`
**الحل**:
- تشغيل كمدير (Run as Administrator)
- فحص صلاحيات المجلد

### 5. المتصفح لا يفتح
**المشكلة**: النظام يعمل لكن المتصفح لا يفتح
**الحل**:
- افتح المتصفح يدوياً
- اذهب إلى: http://localhost:5000

---

## 🌐 عناوين الوصول

- **الرئيسي**: http://localhost:5000
- **البديل**: http://127.0.0.1:5000
- **الشبكة**: http://***********:5000

---

## 👤 بيانات تسجيل الدخول

### المستخدم الافتراضي
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### مستخدمين إضافيين
- **اسم المستخدم**: `user1`
- **كلمة المرور**: `password123`

- **اسم المستخدم**: `manager`
- **كلمة المرور**: `manager123`

---

## 🔍 فحص حالة النظام

### فحص سريع
```bash
python -c "from app import create_app; print('النظام يعمل')"
```

### فحص المنفذ
```bash
# Windows
netstat -an | findstr :5000

# Linux/Mac
lsof -i :5000
```

### فحص قاعدة البيانات
```bash
python -c "
import sqlite3
conn = sqlite3.connect('instance/postal_box_management.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM users')
print(f'عدد المستخدمين: {cursor.fetchone()[0]}')
conn.close()
"
```

---

## 🛠️ أدوات الصيانة

### 1. تنظيف شامل
```bash
python clean_system.py
```

### 2. فحص شامل
```bash
python system_check.py
```

### 3. تشخيص متقدم
```bash
python diagnose_system.py
```

### 4. إعادة تهيئة قاعدة البيانات
```bash
python init_database.py
```

---

## 📋 قائمة فحص سريعة

- [ ] Python مثبت (3.8+)
- [ ] المكتبات مثبتة (`pip install -r requirements.txt`)
- [ ] المنفذ 5000 متاح
- [ ] قاعدة البيانات موجودة (`instance/postal_box_management.db`)
- [ ] الملفات الأساسية موجودة (`app.py`, `run.py`, `config.py`)
- [ ] صلاحيات الكتابة متوفرة

---

## 🆘 إذا استمرت المشاكل

### 1. إعادة تثبيت المتطلبات
```bash
pip uninstall -r requirements.txt -y
pip install -r requirements.txt
```

### 2. إعادة إنشاء البيئة
```bash
# إنشاء بيئة افتراضية جديدة
python -m venv venv
venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 3. فحص سجلات الأخطاء
- راجع رسائل الخطأ في Terminal
- فحص ملفات السجلات في مجلد `logs/`

### 4. إعادة تشغيل الكمبيوتر
أحياناً تحل إعادة التشغيل مشاكل المنافذ والذاكرة

---

## 📞 معلومات إضافية

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11, Linux, macOS
- **Python**: 3.8 أو أحدث
- **الذاكرة**: 512 MB RAM
- **المساحة**: 100 MB مساحة فارغة

### الملفات المهمة
- `app.py` - التطبيق الرئيسي
- `config.py` - إعدادات النظام
- `instance/postal_box_management.db` - قاعدة البيانات
- `requirements.txt` - قائمة المكتبات

---

## ✅ خلاصة

**النظام يعمل بشكل مثالي!** إذا كنت تواجه مشاكل:

1. جرب `python simple_start.py`
2. تأكد من تثبيت المتطلبات
3. فحص المنفذ 5000
4. استخدم أدوات التشخيص المتوفرة

**العنوان**: http://localhost:5000  
**المستخدم**: admin  
**كلمة المرور**: admin123
