@echo off
chcp 65001 >nul
title نظام إدارة الصناديق البريدية

echo.
echo ============================================================
echo           🚀 نظام إدارة الصناديق البريدية
echo ============================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo 💡 يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
echo.

REM التحقق من وجود الملفات الأساسية
if not exist "app.py" (
    echo ❌ ملف app.py غير موجود
    pause
    exit /b 1
)

if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ الملفات الأساسية موجودة
echo.

REM تثبيت المتطلبات إذا لزم الأمر
echo 📦 فحص المتطلبات...
pip install -r requirements.txt --quiet
if errorlevel 1 (
    echo ⚠️ مشكلة في تثبيت المتطلبات
    echo 🔄 محاولة التثبيت مرة أخرى...
    pip install -r requirements.txt
)

echo ✅ المتطلبات جاهزة
echo.

REM تشغيل النظام
echo 🚀 بدء تشغيل النظام...
echo.
python start_system.py

echo.
echo 👋 تم إنهاء النظام
pause
